import numpy as np
from scipy.interpolate import RegularGridInterpolator
import numba

class CalcolatoreAnticipoAccensione:
    def __init__(self):
        # Definizione assi mappa 16x16
        self.asse_giri = np.linspace(800, 8000, 16)
        self.asse_carico = np.linspace(0, 100, 16)
        self.mappa_anticipo_base = np.zeros((16, 16))
        
        # Parametri Wiebe
        self.wiebe_a = 6.908
        self.wiebe_m = 2.2
        
        # Parametri velocità fiamma
        self.SL0 = 0.37  # m/s
        self.alpha = 1.75
        self.beta = -0.16
        
        # Parametri detonazione
        self.A_knock = 1e-6
        self.Ea_knock = 15000  # cal/mol
        self.n_knock = -1.7
        
        # Coefficienti mappa anticipo
        self.coeff_anticipo = {
            'a0': 15, 'a1': 0.001, 'a2': -10,
            'a3': -1e-7, 'a4': 5, 'a5': 0.002
        }
        
        # Interpolatore ottimizzato
        self.interpolatore = RegularGridInterpolator(
            (self.asse_giri, self.asse_carico), 
            self.mappa_anticipo_base.T,
            method='linear', 
            bounds_error=False, 
            fill_value=10.0
        )
    
    def calcola_anticipo_ottimale(self, giri, carico, temp_refrigerante, 
                                  temp_aspirazione, pressione_barometrica):
        """
        Calcola l'anticipo di accensione ottimale
        
        Parametri:
        - giri: giri motore (RPM)
        - carico: carico motore (%)
        - temp_refrigerante: temperatura refrigerante (°C)
        - temp_aspirazione: temperatura aria aspirazione (°C)
        - pressione_barometrica: pressione atmosferica (kPa)
        
        Ritorna: anticipo ottimale (gradi PMS)
        """
        # Anticipo base da mappa
        anticipo_base = self.interpolatore([giri, carico])[0]
        
        # Correzioni ambientali
        correzione_temp = self.correzione_temperatura(
            temp_refrigerante, temp_aspirazione
        )
        correzione_pressione = self.correzione_pressione(
            pressione_barometrica
        )
        
        # Margine di sicurezza detonazione
        margine_knock = self.calcola_margine_knock(giri, carico)
        
        # Anticipo finale
        anticipo_finale = (anticipo_base + correzione_temp + 
                          correzione_pressione - margine_knock)
        
        # Limiti di sicurezza
        return np.clip(anticipo_finale, 5, 45)
    
    def correzione_temperatura(self, temp_refrigerante, temp_aspirazione):
        """Calcola correzioni per temperatura"""
        correzione_refrigerante = 0.2 * (90 - temp_refrigerante)
        correzione_aspirazione = -0.3 * (temp_aspirazione - 25)
        return correzione_refrigerante + correzione_aspirazione
    
    def correzione_pressione(self, pressione_barometrica):
        """Calcola correzione per pressione atmosferica"""
        return (101.3 - pressione_barometrica) * 0.1
    
    def calcola_margine_knock(self, giri, carico):
        """Calcola margine di sicurezza per detonazione"""
        # Margine più alto per carichi elevati
        margine_base = 2.0
        if carico > 80:
            margine_base += (carico - 80) * 0.1
        if giri > 6000:
            margine_base += (giri - 6000) * 0.0005
        return margine_base
    
    def genera_mappa_16x16(self, parametri_motore):
        """
        Genera mappa 16x16 per un motore specifico
        
        Parametri:
        - parametri_motore: dizionario con parametri motore
        """
        for i, carico in enumerate(self.asse_carico):
            for j, giri in enumerate(self.asse_giri):
                # Calcolo anticipo ottimale per punto mappa
                anticipo = self.calcola_anticipo_base(
                    giri, carico, parametri_motore
                )
                self.mappa_anticipo_base[i, j] = anticipo
        
        # Aggiorna interpolatore
        self.interpolatore = RegularGridInterpolator(
            (self.asse_giri, self.asse_carico), 
            self.mappa_anticipo_base.T,
            method='linear'
        )
    
    @numba.jit
    def calcola_anticipo_base(self, giri, carico, parametri):
        """Calcolo anticipo base ottimizzato con Numba"""
        # Formula empirica basata su correlazioni validate
        anticipo = (self.coeff_anticipo['a0'] + 
                   self.coeff_anticipo['a1'] * giri + 
                   self.coeff_anticipo['a2'] * carico + 
                   self.coeff_anticipo['a3'] * giri**2 + 
                   self.coeff_anticipo['a4'] * carico**2 + 
                   self.coeff_anticipo['a5'] * giri * carico)
        
        # Correzione per rapporto di compressione
        rc = parametri.get('rapporto_compressione', 10.0)
        correzione_rc = (rc - 10.0) * -1.5
        
        return anticipo + correzione_rc
    
    def visualizza_mappa(self):
        """Visualizza mappa anticipo 3D"""
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        
        fig = plt.figure(figsize=(12, 8))
        ax = fig.add_subplot(111, projection='3d')
        
        GIRI, CARICO = np.meshgrid(self.asse_giri, self.asse_carico)
        
        surf = ax.plot_surface(GIRI, CARICO, self.mappa_anticipo_base, 
                              cmap='viridis', alpha=0.8)
        ax.set_xlabel('Giri (RPM)')
        ax.set_ylabel('Carico (%)')
        ax.set_zlabel('Anticipo (°PMS)')
        ax.set_title('Mappa Anticipo Accensione')
        
        plt.colorbar(surf)
        plt.show()