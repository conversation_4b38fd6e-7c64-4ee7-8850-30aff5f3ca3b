class MotoreBenzina4T:
    def __init__(self, configurazione):
        self.config = configurazione
        self.calcolatore = CalcolatoreAnticipoAccensione()
        self.setup_parametri()
    
    def setup_parametri(self):
        """Configura parametri specifici motore"""
        # Parametri geometrici
        self.alesaggio = self.config.get('alesaggio', 86)  # mm
        self.corsa = self.config.get('corsa', 86)  # mm
        self.rapporto_compressione = self.config.get('rapporto_compressione', 10.0)
        self.cilindrata = self.config.get('cilindrata', 2000)  # cc
        
        # Parametri carburante
        self.numero_ottano = self.config.get('numero_ottano', 95)
        self.rapporto_stechiometrico = self.config.get('rapporto_stechiometrico', 14.7)
        
        # Parametri aspirazione
        self.tipo_aspirazione = self.config.get('tipo_aspirazione', 'aspirato')
        self.pressione_sovralimentazione = self.config.get('pressione_sovralimentazione', 0)
        
        # Genera mappa specifica
        self.genera_mappa_motore()
    
    def genera_mappa_motore(self):
        """Genera mappa ottimizzata per questo motore"""
        parametri = {
            'rapporto_compressione': self.rapporto_compressione,
            'numero_ottano': self.numero_ottano,
            'tipo_aspirazione': self.tipo_aspirazione,
            'pressione_sovralimentazione': self.pressione_sovralimentazione
        }
        
        self.calcolatore.genera_mappa_16x16(parametri)