# Configurazione motore esempio
config_motore = {
    'alesaggio': 86,  # mm
    'corsa': 86,      # mm
    'rapporto_compressione': 10.5,
    'cilindrata': 2000,  # cc
    'numero_ottano': 95,
    'tipo_aspirazione': 'aspirato',
    'pressione_sovralimentazione': 0
}

# Creazione motore
motore = MotoreBenzina4T(config_motore)

# Calcolo anticipo per condizione specifica
anticipo = motore.calcolatore.calcola_anticipo_ottimale(
    giri=3000,
    carico=75,
    temp_refrigerante=90,
    temp_aspirazione=30,
    pressione_barometrica=101.3
)

print(f"Anticipo ottimale: {anticipo:.1f}° PMS")

# Visualizzazione mappa
motore.calcolatore.visualizza_mappa()